# 儿童识字微信小程序

## 项目简介

这是一个专为3-8岁儿童设计的识字学习微信小程序，基于原HTML原型转换而来。小程序实现了完整的学习流程：学习 → 复习 → 回顾，帮助孩子系统性地学习汉字。

## 功能特点

### 核心学习流程
- **字词输入**：每日输入6个要学习的字词
- **学习阶段**：逐个展示字词，家长引导学习
- **复习阶段**：孩子判断是否认识字词（✅/❌）
- **回顾阶段**：重点复习不认识的字词

### 设计特色
- **儿童友好**：色彩丰富、操作简单、界面可爱
- **微信原生**：使用微信小程序官方组件和API
- **本地存储**：支持离线学习，数据本地保存
- **家长参与**：界面设计考虑成人操作便利性

## 项目结构

```
wx/
├── app.js                  # 小程序入口逻辑
├── app.json               # 小程序配置
├── app.wxss               # 全局样式
├── sitemap.json           # 搜索配置
├── project.config.json    # 项目配置
├── pages/                 # 页面目录
│   ├── home/             # 首页
│   │   ├── home.wxml     # 页面结构
│   │   ├── home.wxss     # 页面样式
│   │   ├── home.js       # 页面逻辑
│   │   └── home.json     # 页面配置
│   ├── input/            # 字词输入页
│   ├── learn/            # 学习模式页
│   ├── review/           # 复习模式页
│   ├── recall/           # 回顾模式页
│   ├── progress/         # 学习记录页
│   ├── settings/         # 设置页面
│   └── complete/         # 完成页面
└── utils/                # 工具函数（可选）
```

## 页面说明

### 1. 首页 (home)
- **功能**：学习入口和进度展示
- **特色**：渐变背景、统计卡片、大按钮设计
- **导航**：底部Tab导航的主页

### 2. 字词输入 (input)
- **功能**：每日6个字词输入
- **特色**：编号输入框、学习小贴士
- **验证**：至少输入3个字词才能开始学习

### 3. 学习模式 (learn)
- **功能**：字词展示和图片辅助
- **特色**：大字体显示、进度条、学习指导
- **交互**：上一个/下一个字词切换

### 4. 复习模式 (review)
- **功能**：认识判断（✅/❌）
- **特色**：大按钮选择、实时统计
- **逻辑**：记录认识/不认识的字词

### 5. 回顾模式 (recall)
- **功能**：重点复习不认识的字词
- **特色**：难点标记、记忆小贴士
- **指导**：详细的练习步骤

### 6. 学习记录 (progress)
- **功能**：学习历史和统计分析
- **特色**：成就展示、历史记录、字词标签
- **数据**：累计学习字词、连续天数等

### 7. 设置页面 (settings)
- **功能**：个人信息和应用设置
- **特色**：开关控件、分组设置
- **管理**：数据导入导出、清空数据

### 8. 完成页面 (complete)
- **功能**：学习结束庆祝
- **特色**：动画效果、统计总结、鼓励话语
- **分享**：支持分享学习成果

## 技术实现

### 前端技术
- **WXML**：微信小程序标记语言
- **WXSS**：微信小程序样式语言
- **JavaScript**：页面逻辑和数据处理
- **微信API**：本地存储、页面导航等

### 数据存储
- **wx.setStorageSync**：同步存储学习数据
- **wx.getStorageSync**：同步读取学习数据
- **本地化设计**：所有数据存储在本地，保护隐私

### 设计规范
- **色彩主题**：每个页面使用不同的渐变背景
- **字体大小**：使用rpx单位，适配不同设备
- **交互反馈**：按钮点击效果、页面切换动画
- **无障碍设计**：考虑儿童使用习惯

## 开发指南

### 环境要求
1. 微信开发者工具
2. 微信小程序开发账号
3. 基础的前端开发知识

### 本地开发
1. 下载并安装微信开发者工具
2. 导入项目目录 `wx/`
3. 配置小程序AppID（在project.config.json中）
4. 点击编译预览

### 部署发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台提交审核
4. 审核通过后发布上线

## 数据结构

### 学习数据 (learnData)
```javascript
{
  totalWords: 0,        // 累计学习字词数
  learnDays: 0,         // 连续学习天数
  todayWords: [],       // 今日学习字词
  learnedWords: [],     // 已学字词列表
  reviewResults: [],    // 复习结果
  unknownWords: [],     // 不认识的字词
  settings: {           // 设置信息
    soundEnabled: true,
    autoNext: false,
    difficulty: 'normal'
  }
}
```

### 用户信息 (userInfo)
```javascript
{
  name: '',            // 孩子姓名
  age: '',             // 年龄范围
  avatar: ''           // 头像（可选）
}
```

## 功能扩展建议

### 短期优化
1. **图片资源**：集成真实的字词配图
2. **音效系统**：添加按钮音效和朗读功能
3. **拼音显示**：集成拼音转换API
4. **字词库**：预置常用字词库

### 长期规划
1. **云端同步**：支持多设备数据同步
2. **家长监控**：添加家长端监控功能
3. **学习报告**：生成详细的学习分析报告
4. **社交功能**：添加学习排行榜和分享

## 注意事项

1. **隐私保护**：所有数据存储在本地，不上传服务器
2. **儿童安全**：界面设计考虑儿童使用安全
3. **家长参与**：需要家长陪同使用，引导学习
4. **适龄设计**：适合3-8岁儿童的认知水平

## 联系方式

如有问题或建议，请联系开发团队。

---

**版本**：v1.0.0  
**更新时间**：2024年  
**开发团队**：儿童教育技术团队
