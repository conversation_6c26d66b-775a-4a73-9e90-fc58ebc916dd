<!--
  字词输入页面
  功能：每日6个字词输入
-->
<view class="container gradient-bg-pink">
  <!-- iOS 状态栏 -->
  <view class="status-bar">
    <text>9:41</text>
    <view class="status-icons">
      <text class="iconfont">📶</text>
      <text class="iconfont">📶</text>
      <text class="iconfont">🔋</text>
    </view>
  </view>
  
  <!-- 页面头部 -->
  <view class="header">
    <text class="header-title">今日学习准备</text>
    <text class="header-subtitle">请输入今天要学习的6个字词</text>
  </view>
  
  <!-- 主要内容 -->
  <scroll-view class="main-content" scroll-y="true">
    <!-- 输入区域 -->
    <view class="input-container card">
      <view class="input-grid">
        <view class="input-item" wx:for="{{wordInputs}}" wx:key="index">
          <view class="input-number">{{index + 1}}</view>
          <input 
            class="input-field" 
            placeholder="请输入第{{index + 1}}个字词" 
            maxlength="4"
            value="{{item}}"
            data-index="{{index}}"
            bindinput="onWordInput"
            bindconfirm="onInputConfirm"
          />
        </view>
      </view>
    </view>
    
    <!-- 提示区域 -->
    <view class="tips-section glass-card">
      <view class="tips-title">
        <text class="icon-text">💡</text>
        <text>学习小贴士</text>
      </view>
      <view class="tips-list">
        <text class="tip-item">• 建议选择孩子感兴趣的字词</text>
        <text class="tip-item">• 可以是生活中常见的物品名称</text>
        <text class="tip-item">• 每个字词建议不超过4个字</text>
        <text class="tip-item">• 可以重复之前学过的字词加深印象</text>
      </view>
    </view>
  </scroll-view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="btn btn-secondary" bindtap="goBack">
      <text class="icon-text">⬅️</text>
      <text>返回</text>
    </button>
    <button class="btn btn-primary" bindtap="startLearning" disabled="{{!canStart}}">
      <text class="icon-text">▶️</text>
      <text>开始学习</text>
    </button>
  </view>
</view>
