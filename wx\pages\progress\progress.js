/**
 * 学习记录页面逻辑
 * 功能：展示学习历史和统计分析
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    learnData: {
      totalWords: 0,
      learnDays: 0
    },
    todayWords: [],
    todayCompleted: false,
    todayProgress: 0,
    todayDate: '',
    historyList: [],
    learnedWords: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('学习记录页加载')
    this.loadProgressData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('学习记录页显示')
    this.loadProgressData()
  },

  /**
   * 加载进度数据
   */
  loadProgressData: function() {
    try {
      // 获取学习数据
      const learnData = app.getLearnData()
      
      // 获取今日数据
      const todayWords = learnData.todayWords || []
      const todayCompleted = wx.getStorageSync('todayCompleted') || false
      const todayProgress = this.calculateTodayProgress(todayWords, todayCompleted)
      
      // 获取学习历史
      const historyList = this.getHistoryList()
      
      // 获取已学字词
      const learnedWords = learnData.learnedWords || []
      
      this.setData({
        learnData: {
          totalWords: learnData.totalWords || 0,
          learnDays: learnData.learnDays || 0
        },
        todayWords: todayWords,
        todayCompleted: todayCompleted,
        todayProgress: todayProgress,
        todayDate: this.formatDate(new Date()),
        historyList: historyList,
        learnedWords: learnedWords
      })
    } catch (error) {
      console.error('加载进度数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 计算今日进度
   */
  calculateTodayProgress: function(todayWords, todayCompleted) {
    if (todayWords.length === 0) {
      return 0
    }
    
    if (todayCompleted) {
      return 100
    }
    
    // 检查学习进度
    const learnProgress = wx.getStorageSync('learnProgress')
    const reviewProgress = wx.getStorageSync('reviewProgress')
    const recallProgress = wx.getStorageSync('recallProgress')
    
    let progress = 0
    
    if (learnProgress && learnProgress.currentIndex >= 0) {
      progress += 30 // 学习阶段完成30%
    }
    
    if (reviewProgress && reviewProgress.currentIndex >= 0) {
      progress += 40 // 复习阶段完成40%
    }
    
    if (recallProgress && recallProgress.currentIndex >= 0) {
      progress += 30 // 回顾阶段完成30%
    }
    
    return Math.min(progress, 100)
  },

  /**
   * 获取学习历史列表
   */
  getHistoryList: function() {
    try {
      const historyData = wx.getStorageSync('learnHistory') || []
      return historyData.slice(0, 10) // 只显示最近10条记录
    } catch (error) {
      console.error('获取学习历史失败:', error)
      return []
    }
  },

  /**
   * 格式化日期
   */
  formatDate: function(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 开始学习
   */
  startLearning: function() {
    wx.switchTab({
      url: '/pages/home/<USER>',
      success: () => {
        console.log('跳转到首页成功')
      }
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadProgressData()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面滚动到底部
   */
  onReachBottom: function() {
    // 可以在这里加载更多历史记录
    console.log('滚动到底部')
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const { totalWords, learnDays } = this.data.learnData
    return {
      title: `我已经学习了${totalWords}个字词，坚持了${learnDays}天！`,
      path: '/pages/progress/progress'
    }
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    const { totalWords, learnDays } = this.data.learnData
    return {
      title: `儿童识字 - 已学习${totalWords}个字词，坚持${learnDays}天`,
      imageUrl: '/images/share-progress.png'
    }
  }
})
