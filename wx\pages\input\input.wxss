/**
 * 字词输入页面样式
 */

.container {
  height: 100vh;
  overflow: hidden;
}

.header {
  padding: 40rpx;
  text-align: center;
  color: white;
}

.header-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.header-subtitle {
  display: block;
  font-size: 32rpx;
  opacity: 0.9;
}

.main-content {
  height: calc(100vh - 94rpx - 200rpx - 160rpx);
  padding: 0 40rpx;
}

.input-container {
  background: white;
  border-radius: 40rpx;
  padding: 50rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.1);
}

.input-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.input-item {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.input-number {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 32rpx;
  flex-shrink: 0;
}

.input-field {
  flex: 1;
  border: 4rpx solid #f0f0f0;
  border-radius: 24rpx;
  padding: 30rpx;
  font-size: 36rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #f093fb;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(240, 147, 251, 0.1);
}

.tips-section {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.tips-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.tips-list {
  color: white;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.6;
}

.tip-item {
  display: block;
  margin-bottom: 10rpx;
}

.action-buttons {
  padding: 40rpx;
  display: flex;
  gap: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-secondary {
  flex: 1;
  background: rgba(255,255,255,0.2);
  color: white;
  border: 4rpx solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20rpx);
}

.btn-primary {
  flex: 2;
  background: white;
  color: #f5576c;
}

.btn-primary[disabled] {
  background: rgba(255,255,255,0.5);
  color: rgba(245, 87, 108, 0.5);
}

.icon-text {
  font-size: inherit;
}
