/**
 * 首页逻辑
 * 功能：展示学习统计，提供学习入口
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    learnData: {
      totalWords: 0,
      learnDays: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('首页加载')
    this.loadLearnData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('首页显示')
    this.loadLearnData()
  },

  /**
   * 加载学习数据
   */
  loadLearnData: function() {
    try {
      const learnData = app.getLearnData()
      this.setData({
        learnData: {
          totalWords: learnData.totalWords || 0,
          learnDays: learnData.learnDays || 0
        }
      })
    } catch (error) {
      console.error('加载学习数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 开始学习
   */
  startLearning: function() {
    console.log('开始学习')
    
    // 检查今日是否已完成学习
    const todayCompleted = wx.getStorageSync('todayCompleted')
    if (todayCompleted) {
      wx.showModal({
        title: '提示',
        content: '今日学习已完成，是否重新开始？',
        success: (res) => {
          if (res.confirm) {
            this.navigateToInput()
          }
        }
      })
    } else {
      this.navigateToInput()
    }
  },

  /**
   * 跳转到字词输入页
   */
  navigateToInput: function() {
    wx.navigateTo({
      url: '/pages/input/input',
      success: () => {
        console.log('跳转到字词输入页成功')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 查看学习记录
   */
  viewProgress: function() {
    console.log('查看学习记录')
    wx.switchTab({
      url: '/pages/progress/progress',
      success: () => {
        console.log('跳转到学习记录页成功')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '儿童识字 - 让学习变得简单有趣',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share.png'
    }
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '儿童识字 - 让学习变得简单有趣',
      imageUrl: '/images/share.png'
    }
  }
})
