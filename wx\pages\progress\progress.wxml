<!--
  学习记录页面
  功能：学习历史和统计分析
-->
<view class="container gradient-bg-blue">
  <!-- iOS 状态栏 -->
  <view class="status-bar">
    <text>9:41</text>
    <view class="status-icons">
      <text class="iconfont">📶</text>
      <text class="iconfont">📶</text>
      <text class="iconfont">🔋</text>
    </view>
  </view>
  
  <!-- 主要内容 -->
  <scroll-view class="main-content" scroll-y="true">
    <!-- 成就展示 -->
    <view class="achievement-section">
      <view class="achievement-card glass-card">
        <view class="achievement-title">
          <text class="icon-text">🏆</text>
          <text>学习成就</text>
        </view>
        <view class="achievement-stats">
          <view class="stat-item">
            <text class="stat-number">{{learnData.totalWords}}</text>
            <text class="stat-label">累计学习字词</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{learnData.learnDays}}</text>
            <text class="stat-label">连续学习天数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{todayProgress}}%</text>
            <text class="stat-label">今日完成度</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 今日学习 -->
    <view class="today-section">
      <view class="section-title">
        <text class="icon-text">📅</text>
        <text>今日学习</text>
      </view>
      <view class="today-card card" wx:if="{{todayWords.length > 0}}">
        <view class="today-header">
          <text class="today-date">{{todayDate}}</text>
          <view class="today-status {{todayCompleted ? 'completed' : 'pending'}}">
            <text>{{todayCompleted ? '已完成' : '进行中'}}</text>
          </view>
        </view>
        <view class="word-tags">
          <view class="word-tag" wx:for="{{todayWords}}" wx:key="index">
            <text>{{item}}</text>
          </view>
        </view>
      </view>
      <view class="empty-state" wx:else>
        <text class="empty-icon">📝</text>
        <text class="empty-text">今日还没有开始学习</text>
        <button class="btn btn-primary btn-small" bindtap="startLearning">开始学习</button>
      </view>
    </view>
    
    <!-- 学习历史 -->
    <view class="history-section">
      <view class="section-title">
        <text class="icon-text">📊</text>
        <text>学习历史</text>
      </view>
      <view class="history-list" wx:if="{{historyList.length > 0}}">
        <view class="history-item card" wx:for="{{historyList}}" wx:key="index">
          <view class="history-header">
            <text class="history-date">{{item.date}}</text>
            <view class="history-stats">
              <text class="history-count">{{item.words.length}}个字词</text>
            </view>
          </view>
          <view class="word-tags">
            <view class="word-tag small" wx:for="{{item.words}}" wx:key="wordIndex" wx:for-item="word">
              <text>{{word}}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="empty-state" wx:else>
        <text class="empty-icon">📈</text>
        <text class="empty-text">暂无学习历史</text>
      </view>
    </view>
    
    <!-- 已学字词库 -->
    <view class="learned-section">
      <view class="section-title">
        <text class="icon-text">📚</text>
        <text>已学字词库</text>
      </view>
      <view class="learned-words" wx:if="{{learnedWords.length > 0}}">
        <view class="word-tags">
          <view class="word-tag learned" wx:for="{{learnedWords}}" wx:key="index">
            <text>{{item}}</text>
          </view>
        </view>
      </view>
      <view class="empty-state" wx:else>
        <text class="empty-icon">📖</text>
        <text class="empty-text">还没有学习过字词</text>
      </view>
    </view>
  </scroll-view>
</view>
