/**
 * 儿童识字小程序 - 应用程序入口
 * 管理全局数据和生命周期
 */
App({
  /**
   * 应用启动时的回调函数
   */
  onLaunch: function () {
    console.log('儿童识字小程序启动')
    
    // 初始化本地存储
    this.initStorage()
    
    // 检查小程序版本更新
    this.checkUpdate()
  },

  /**
   * 应用显示时的回调函数
   */
  onShow: function () {
    console.log('应用显示')
  },

  /**
   * 应用隐藏时的回调函数
   */
  onHide: function () {
    console.log('应用隐藏')
  },

  /**
   * 初始化本地存储
   */
  initStorage: function() {
    try {
      // 初始化学习数据
      const learnData = wx.getStorageSync('learnData')
      if (!learnData) {
        wx.setStorageSync('learnData', {
          totalWords: 0,
          learnDays: 0,
          todayWords: [],
          learnedWords: [],
          reviewResults: [],
          settings: {
            soundEnabled: true,
            autoNext: false,
            difficulty: 'normal'
          }
        })
      }
      
      // 初始化今日学习状态
      const today = new Date().toDateString()
      const lastLearnDate = wx.getStorageSync('lastLearnDate')
      if (lastLearnDate !== today) {
        wx.setStorageSync('lastLearnDate', today)
        wx.setStorageSync('todayCompleted', false)
      }
    } catch (error) {
      console.error('初始化存储失败:', error)
    }
  },

  /**
   * 检查小程序更新
   */
  checkUpdate: function() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate(function (res) {
        console.log('检查更新结果:', res.hasUpdate)
      })
      
      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: function (res) {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(function () {
        console.log('新版本下载失败')
      })
    }
  },

  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    systemInfo: null
  },

  /**
   * 获取学习数据
   */
  getLearnData: function() {
    try {
      return wx.getStorageSync('learnData') || {}
    } catch (error) {
      console.error('获取学习数据失败:', error)
      return {}
    }
  },

  /**
   * 保存学习数据
   */
  saveLearnData: function(data) {
    try {
      wx.setStorageSync('learnData', data)
      return true
    } catch (error) {
      console.error('保存学习数据失败:', error)
      return false
    }
  },

  /**
   * 获取系统信息
   */
  getSystemInfo: function() {
    if (!this.globalData.systemInfo) {
      this.globalData.systemInfo = wx.getSystemInfoSync()
    }
    return this.globalData.systemInfo
  }
})
