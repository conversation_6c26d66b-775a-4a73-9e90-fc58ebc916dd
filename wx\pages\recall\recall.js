/**
 * 回顾模式页面逻辑
 * 功能：重点复习不认识的字词
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    unknownWords: [], // 不认识的字词
    currentIndex: 0, // 当前字词索引
    currentWord: '', // 当前字词
    currentPinyin: '', // 当前字词拼音
    totalWords: 0, // 总字词数
    progressPercent: 0, // 进度百分比
    isLastWord: false // 是否是最后一个字词
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('回顾模式页加载')
    this.loadUnknownWords()
  },

  /**
   * 加载不认识的字词
   */
  loadUnknownWords: function() {
    try {
      const learnData = app.getLearnData()
      const unknownWords = learnData.unknownWords || []
      
      if (unknownWords.length === 0) {
        wx.showModal({
          title: '太棒了！',
          content: '没有需要回顾的字词，今日学习已完成！',
          showCancel: false,
          success: (res) => {
            wx.redirectTo({
              url: '/pages/complete/complete'
            })
          }
        })
        return
      }

      this.setData({
        unknownWords: unknownWords,
        totalWords: unknownWords.length,
        currentWord: unknownWords[0] || '',
        currentPinyin: this.getPinyin(unknownWords[0] || ''),
        progressPercent: Math.round((1 / unknownWords.length) * 100),
        isLastWord: unknownWords.length === 1
      })
    } catch (error) {
      console.error('加载不认识字词失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 获取字词拼音（简单实现）
   */
  getPinyin: function(word) {
    // 这里可以接入拼音转换API
    // 暂时返回空字符串
    return ''
  },

  /**
   * 上一个字词
   */
  previousWord: function() {
    if (this.data.currentIndex > 0) {
      const newIndex = this.data.currentIndex - 1
      this.updateCurrentWord(newIndex)
    }
  },

  /**
   * 下一个字词
   */
  nextWord: function() {
    if (this.data.isLastWord) {
      // 完成回顾
      this.completeRecall()
    } else {
      const newIndex = this.data.currentIndex + 1
      this.updateCurrentWord(newIndex)
    }
  },

  /**
   * 更新当前字词
   */
  updateCurrentWord: function(index) {
    const { unknownWords, totalWords } = this.data
    
    if (index >= 0 && index < totalWords) {
      const currentWord = unknownWords[index]
      const progressPercent = Math.round(((index + 1) / totalWords) * 100)
      
      this.setData({
        currentIndex: index,
        currentWord: currentWord,
        currentPinyin: this.getPinyin(currentWord),
        progressPercent: progressPercent,
        isLastWord: index === totalWords - 1
      })
    }
  },

  /**
   * 完成回顾
   */
  completeRecall: function() {
    try {
      // 更新学习数据
      const learnData = app.getLearnData()
      
      // 标记今日学习完成
      wx.setStorageSync('todayCompleted', true)
      
      // 更新学习天数
      const today = new Date().toDateString()
      const lastLearnDate = wx.getStorageSync('lastLearnDate')
      if (lastLearnDate !== today) {
        learnData.learnDays = (learnData.learnDays || 0) + 1
        wx.setStorageSync('lastLearnDate', today)
      }
      
      app.saveLearnData(learnData)
      
      // 显示完成提示
      wx.showModal({
        title: '回顾完成！',
        content: `已完成 ${this.data.totalWords} 个重点字词的回顾练习。今日学习全部完成！`,
        showCancel: false,
        success: (res) => {
          wx.redirectTo({
            url: '/pages/complete/complete'
          })
        }
      })
    } catch (error) {
      console.error('完成回顾失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 页面返回事件
   */
  onUnload: function() {
    // 保存回顾进度
    try {
      wx.setStorageSync('recallProgress', {
        currentIndex: this.data.currentIndex,
        timestamp: Date.now()
      })
    } catch (error) {
      console.error('保存回顾进度失败:', error)
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: `正在回顾：${this.data.currentWord}`,
      path: '/pages/recall/recall'
    }
  }
})
