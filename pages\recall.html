<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回顾模式</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 47px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            backdrop-filter: blur(10px);
        }
        
        .header-title {
            font-size: 20px;
            font-weight: bold;
        }
        
        .progress-info {
            text-align: center;
            font-size: 16px;
            font-weight: 600;
        }
        
        .main-content {
            height: calc(100vh - 47px - 80px - 80px - 100px); /* Adjusted to account for nav bar */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .recall-card {
            background: white;
            border-radius: 25px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            width: 100%;
            max-width: 300px;
            border: 3px solid #ff6b6b;
        }
        
        .difficulty-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 20px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .word-image {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 50px;
            color: white;
            background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop&crop=center');
            background-size: cover;
            background-position: center;
        }
        
        .word-text {
            font-size: 48px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            letter-spacing: 2px;
        }
        
        .word-description {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        
        .recall-tips {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 12px;
            padding: 15px;
            font-size: 14px;
            color: #856404;
            line-height: 1.5;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: white;
            width: 66%; /* Example progress */
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .action-buttons {
            padding: 20px;
            display: flex;
            gap: 15px;
        }
        
        .btn-secondary {
            flex: 1;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            flex: 2;
            background: white;
            color: #a8edea; /* Match gradient color */
            border: none;
            border-radius: 25px;
            padding: 18px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        /* Removed .bottom-nav styles as we will use Tailwind */
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div style="display: flex; gap: 5px;">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>
    
    <!-- 页面头部 -->
    <div class="header">
        <div class="header-left">
            <div class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </div>
            <div class="header-title">回顾模式</div>
        </div>
        <div class="progress-info">2 / 3</div>
    </div>
    
    <!-- 进度条 -->
    <div style="padding: 0 20px;">
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <div class="recall-card">
            <div class="difficulty-badge">
                <i class="fas fa-brain"></i> 常考易错
            </div>
            <div class="word-image" id="wordImage"></div>
            <div class="word-text" id="wordText">橙子</div>
            <div class="word-description" id="wordDescription">一种橙色的柑橘类水果，味道酸甜，富含维生素C。</div>
            <div class="recall-tips">
                <i class="fas fa-lightbulb"></i> 回忆小贴士：橙子的颜色是橙色的，想一想橙子酸酸甜甜的味道。
            </div>
        </div>
    </div>

    <div class="action-buttons">
        <button class="btn-secondary" onclick="prevWord()">
            <i class="fas fa-arrow-left"></i> 上一个
        </button>
        <button class="btn-primary" onclick="nextWord()">
            下一个 <i class="fas fa-arrow-right"></i>
        </button>
    </div>

    <!-- Bottom Navigation Bar -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white shadow-t h-20 flex justify-around items-center z-50">
        <a href="../index.html" class="nav-item flex flex-col items-center justify-center text-gray-500 hover:text-blue-500 w-1/4 h-full">
            <i class="fas fa-home text-2xl mb-1"></i>
            <span class="text-xs font-medium">首页</span>
        </a>
        <a href="./learn.html" class="nav-item flex flex-col items-center justify-center text-gray-500 hover:text-blue-500 w-1/4 h-full">
            <i class="fas fa-book-open text-2xl mb-1"></i>
            <span class="text-xs font-medium">学习</span>
        </a>
        <a href="./recall.html" class="nav-item flex flex-col items-center justify-center text-blue-500 w-1/4 h-full">
            <i class="fas fa-puzzle-piece text-2xl mb-1"></i>
            <span class="text-xs font-medium">复习</span>
        </a>
        <a href="./profile.html" class="nav-item flex flex-col items-center justify-center text-gray-500 hover:text-blue-500 w-1/4 h-full">
            <i class="fas fa-user text-2xl mb-1"></i>
            <span class="text-xs font-medium">我的</span>
        </a>
    </nav>

    <script>
        const words = [
            {
                text: "苹果",
                imageChar: "🍎",
                description: "一种常见的水果，通常是红色或绿色，味道甜美，富含维生素。",
                tips: "苹果是红色的，圆圆的，可以做苹果派。",
                difficulty: "简单",
                imgUrl: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=200&h=200&fit=crop&crop=center' // Example image
            },
            {
                text: "香蕉",
                imageChar: "🍌",
                description: "一种常见的热带水果，黄色，弯曲形状，味道香甜。",
                tips: "香蕉是黄色的，长长的，猴子喜欢吃。",
                difficulty: "简单",
                imgUrl: 'https://images.unsplash.com/photo-1528825871115-3581a5387919?w=200&h=200&fit=crop&crop=center'
            },
            {
                text: "橙子",
                imageChar: "🍊",
                description: "一种橙色的柑橘类水果，味道酸甜，富含维生素C。",
                tips: "橙子的颜色是橙色的，想一想橙子酸酸甜甜的味道。",
                difficulty: "中等",
                imgUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop&crop=center'
            },
            {
                text: "草莓",
                imageChar: "🍓",
                description: "一种红色的水果，表面有很多小籽，味道酸甜可口。",
                tips: "草莓是红色的，上面有很多小点点，可以做草莓蛋糕。",
                difficulty: "中等",
                imgUrl: 'https://images.unsplash.com/photo-1587393855524-087f83d95bc9?w=200&h=200&fit=crop&crop=center'
            },
            {
                text: "西瓜",
                imageChar: "🍉",
                description: "一种夏季常见的大型水果，外皮绿色，果肉红色或黄色，水分充足。",
                tips: "西瓜是夏天吃的，圆圆的，绿色的皮，红色的瓤。",
                difficulty: "困难",
                imgUrl: 'https://images.unsplash.com/photo-1589984662646-e7b2e399727c?w=200&h=200&fit=crop&crop=center'
            },
            {
                text: "葡萄",
                imageChar: "🍇",
                description: "一种成串生长的水果，有紫色、绿色等多种颜色，可以酿酒。",
                tips: "葡萄是一串一串的，有紫色的，也有绿色的，可以做葡萄酒。",
                difficulty: "困难",
                imgUrl: 'https://images.unsplash.com/photo-1596363609149-923c8561541c?w=200&h=200&fit=crop&crop=center'
            }
        ];

        let currentIndex = 2; // Start with '橙子'

        const wordImageElement = document.getElementById('wordImage');
        const wordTextElement = document.getElementById('wordText');
        const wordDescriptionElement = document.getElementById('wordDescription');
        const recallTipsElement = document.querySelector('.recall-tips');
        const difficultyBadgeElement = document.querySelector('.difficulty-badge');
        const progressFillElement = document.querySelector('.progress-fill');
        const progressInfoElement = document.querySelector('.progress-info');

        function updateWordDisplay() {
            const word = words[currentIndex];
            // wordImageElement.textContent = word.imageChar; // Using background image now
            wordImageElement.style.backgroundImage = `url('${word.imgUrl}')`;
            wordTextElement.textContent = word.text;
            wordDescriptionElement.textContent = word.description;
            recallTipsElement.innerHTML = `<i class="fas fa-lightbulb"></i> 回忆小贴士：${word.tips}`;
            difficultyBadgeElement.innerHTML = `<i class="fas fa-brain"></i> ${word.difficulty}`;
            
            const progressPercentage = ((currentIndex + 1) / words.length) * 100;
            progressFillElement.style.width = `${progressPercentage}%`;
            if (progressInfoElement) { // Check if element exists
                 progressInfoElement.textContent = `${currentIndex + 1} / ${words.length}`;
            }
        }

        function nextWord() {
            currentIndex = (currentIndex + 1) % words.length;
            updateWordDisplay();
        }

        function prevWord() {
            currentIndex = (currentIndex - 1 + words.length) % words.length;
            updateWordDisplay();
        }

        document.addEventListener('DOMContentLoaded', () => {
            updateWordDisplay();
            console.log("Recall page loaded. Initial word displayed.");

            const bottomNav = document.querySelector('.fixed.bottom-0');
            if (bottomNav) {
                console.log("Bottom navigation bar found:", bottomNav);
                const navItems = bottomNav.querySelectorAll('.nav-item');
                console.log(`Found ${navItems.length} nav items.`);
                navItems.forEach((item, index) => {
                    console.log(`Nav item ${index + 1}:`, item);
                    const span = item.querySelector('span');
                    if (span) {
                        console.log(`Span in nav item ${index + 1}:`, span, `Text: '${span.textContent}'`);
                        console.log(`Span computed styles for nav item ${index + 1}:`, window.getComputedStyle(span).display, window.getComputedStyle(span).visibility, window.getComputedStyle(span).opacity);
                    } else {
                        console.log(`No span found in nav item ${index + 1}`);
                    }
                });
            } else {
                console.error("Bottom navigation bar NOT found!");
            }
        });

        function goHome() {
            window.location.href = '../index.html';
        }
    </script>
</body>
</html>
