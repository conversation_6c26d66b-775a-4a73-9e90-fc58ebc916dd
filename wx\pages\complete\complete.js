/**
 * 完成页面逻辑
 * 功能：展示学习完成庆祝和统计
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    todayStats: {
      totalWords: 0,
      knownWords: 0,
      reviewWords: 0,
      accuracy: 0
    },
    encouragementData: {
      icon: '🎉',
      text: '太棒了！',
      subtitle: '今天的学习任务完成了'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('完成页加载')
    this.loadTodayStats()
    this.generateEncouragement()
  },

  /**
   * 加载今日统计
   */
  loadTodayStats: function() {
    try {
      const learnData = app.getLearnData()
      const todayWords = learnData.todayWords || []
      const reviewResults = learnData.reviewResults || []
      const unknownWords = learnData.unknownWords || []
      
      // 计算统计数据
      const totalWords = todayWords.length
      const knownWords = reviewResults.filter(result => result === true).length
      const reviewWords = unknownWords.length
      const accuracy = totalWords > 0 ? Math.round((knownWords / totalWords) * 100) : 0
      
      this.setData({
        todayStats: {
          totalWords,
          knownWords,
          reviewWords,
          accuracy
        }
      })
      
      // 保存今日学习记录到历史
      this.saveTodayToHistory(todayWords, accuracy)
    } catch (error) {
      console.error('加载今日统计失败:', error)
    }
  },

  /**
   * 保存今日学习到历史记录
   */
  saveTodayToHistory: function(todayWords, accuracy) {
    try {
      const today = new Date()
      const dateStr = this.formatDate(today)
      
      // 获取现有历史记录
      let historyList = wx.getStorageSync('learnHistory') || []
      
      // 检查今日记录是否已存在
      const existingIndex = historyList.findIndex(item => item.date === dateStr)
      
      const todayRecord = {
        date: dateStr,
        words: todayWords,
        accuracy: accuracy,
        timestamp: today.getTime()
      }
      
      if (existingIndex >= 0) {
        // 更新现有记录
        historyList[existingIndex] = todayRecord
      } else {
        // 添加新记录
        historyList.unshift(todayRecord)
      }
      
      // 只保留最近30天的记录
      historyList = historyList.slice(0, 30)
      
      wx.setStorageSync('learnHistory', historyList)
    } catch (error) {
      console.error('保存学习历史失败:', error)
    }
  },

  /**
   * 格式化日期
   */
  formatDate: function(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 生成鼓励话语
   */
  generateEncouragement: function() {
    const { accuracy } = this.data.todayStats
    
    let encouragementData = {
      icon: '🎉',
      text: '太棒了！',
      subtitle: '今天的学习任务完成了'
    }
    
    if (accuracy >= 90) {
      encouragementData = {
        icon: '🌟',
        text: '完美表现！',
        subtitle: '你是识字小天才'
      }
    } else if (accuracy >= 70) {
      encouragementData = {
        icon: '👏',
        text: '表现很好！',
        subtitle: '继续保持这个状态'
      }
    } else if (accuracy >= 50) {
      encouragementData = {
        icon: '💪',
        text: '不错的开始！',
        subtitle: '多练习会更棒的'
      }
    } else {
      encouragementData = {
        icon: '🌱',
        text: '每天进步一点点！',
        subtitle: '坚持就是胜利'
      }
    }
    
    this.setData({ encouragementData })
  },

  /**
   * 查看进度
   */
  viewProgress: function() {
    wx.switchTab({
      url: '/pages/progress/progress',
      success: () => {
        console.log('跳转到进度页成功')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 返回首页
   */
  backToHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>',
      success: () => {
        console.log('返回首页成功')
      },
      fail: (error) => {
        console.error('返回失败:', error)
        wx.showToast({
          title: '返回失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 页面卸载时清理
   */
  onUnload: function() {
    // 清理今日学习的临时数据
    try {
      wx.removeStorageSync('learnProgress')
      wx.removeStorageSync('reviewProgress')
      wx.removeStorageSync('recallProgress')
    } catch (error) {
      console.error('清理临时数据失败:', error)
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const { totalWords, accuracy } = this.data.todayStats
    return {
      title: `今天学习了${totalWords}个字词，掌握率${accuracy}%！`,
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-complete.png'
    }
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    const { totalWords, accuracy } = this.data.todayStats
    return {
      title: `儿童识字 - 今天学习${totalWords}个字词，掌握率${accuracy}%`,
      imageUrl: '/images/share-complete.png'
    }
  }
})
