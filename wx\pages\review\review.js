/**
 * 复习模式页面逻辑
 * 功能：判断孩子是否认识字词
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    todayWords: [], // 今日要复习的字词
    currentIndex: 0, // 当前字词索引
    currentWord: '', // 当前字词
    totalWords: 0, // 总字词数
    progressPercent: 0, // 进度百分比
    knownCount: 0, // 认识的字词数
    unknownCount: 0, // 不认识的字词数
    reviewResults: [], // 复习结果
    unknownWords: [] // 不认识的字词列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('复习模式页加载')
    this.loadTodayWords()
  },

  /**
   * 加载今日字词
   */
  loadTodayWords: function() {
    try {
      const learnData = app.getLearnData()
      const todayWords = learnData.todayWords || []
      
      if (todayWords.length === 0) {
        wx.showModal({
          title: '提示',
          content: '没有找到今日学习的字词',
          success: (res) => {
            wx.navigateBack()
          }
        })
        return
      }

      this.setData({
        todayWords: todayWords,
        totalWords: todayWords.length,
        currentWord: todayWords[0] || '',
        progressPercent: 0,
        reviewResults: new Array(todayWords.length).fill(null)
      })
    } catch (error) {
      console.error('加载今日字词失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 标记为认识
   */
  markAsKnown: function() {
    this.recordResult(true)
  },

  /**
   * 标记为不认识
   */
  markAsUnknown: function() {
    this.recordResult(false)
  },

  /**
   * 记录复习结果
   */
  recordResult: function(isKnown) {
    const { currentIndex, reviewResults, todayWords, unknownWords } = this.data
    
    // 更新结果数组
    reviewResults[currentIndex] = isKnown
    
    // 更新统计
    let knownCount = 0
    let unknownCount = 0
    let newUnknownWords = []
    
    reviewResults.forEach((result, index) => {
      if (result === true) {
        knownCount++
      } else if (result === false) {
        unknownCount++
        newUnknownWords.push(todayWords[index])
      }
    })
    
    this.setData({
      reviewResults: reviewResults,
      knownCount: knownCount,
      unknownCount: unknownCount,
      unknownWords: newUnknownWords
    })
    
    // 移动到下一个字词或完成复习
    this.nextWord()
  },

  /**
   * 下一个字词
   */
  nextWord: function() {
    const { currentIndex, totalWords } = this.data
    
    if (currentIndex < totalWords - 1) {
      // 还有字词需要复习
      const newIndex = currentIndex + 1
      const progressPercent = Math.round(((newIndex + 1) / totalWords) * 100)
      
      this.setData({
        currentIndex: newIndex,
        currentWord: this.data.todayWords[newIndex],
        progressPercent: progressPercent
      })
    } else {
      // 复习完成
      this.completeReview()
    }
  },

  /**
   * 完成复习
   */
  completeReview: function() {
    try {
      // 保存复习结果
      const learnData = app.getLearnData()
      learnData.reviewResults = this.data.reviewResults
      learnData.unknownWords = this.data.unknownWords
      app.saveLearnData(learnData)
      
      // 显示复习结果
      const { knownCount, unknownCount, unknownWords } = this.data
      
      if (unknownWords.length > 0) {
        // 有不认识的字词，跳转到回顾页面
        wx.showModal({
          title: '复习完成',
          content: `认识 ${knownCount} 个，不认识 ${unknownCount} 个。现在进入回顾环节重点练习。`,
          showCancel: false,
          success: (res) => {
            wx.redirectTo({
              url: '/pages/recall/recall'
            })
          }
        })
      } else {
        // 全部认识，跳转到完成页面
        wx.showModal({
          title: '太棒了！',
          content: `全部 ${knownCount} 个字词都认识！今日学习完成。`,
          showCancel: false,
          success: (res) => {
            wx.redirectTo({
              url: '/pages/complete/complete'
            })
          }
        })
      }
    } catch (error) {
      console.error('完成复习失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 页面返回事件
   */
  onUnload: function() {
    // 保存复习进度
    try {
      wx.setStorageSync('reviewProgress', {
        currentIndex: this.data.currentIndex,
        reviewResults: this.data.reviewResults,
        timestamp: Date.now()
      })
    } catch (error) {
      console.error('保存复习进度失败:', error)
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: `正在复习：${this.data.currentWord}`,
      path: '/pages/review/review'
    }
  }
})
