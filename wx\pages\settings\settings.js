/**
 * 设置页面逻辑
 * 功能：个人信息和应用设置管理
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      name: '',
      age: ''
    },
    settings: {
      soundEnabled: true,
      autoNext: false,
      difficulty: 'normal'
    },
    difficultyText: '普通'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('设置页加载')
    this.loadSettings()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.loadSettings()
  },

  /**
   * 加载设置数据
   */
  loadSettings: function() {
    try {
      // 加载用户信息
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      // 加载学习数据中的设置
      const learnData = app.getLearnData()
      const settings = learnData.settings || {
        soundEnabled: true,
        autoNext: false,
        difficulty: 'normal'
      }
      
      this.setData({
        userInfo: userInfo,
        settings: settings,
        difficultyText: this.getDifficultyText(settings.difficulty)
      })
    } catch (error) {
      console.error('加载设置失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 获取难度文本
   */
  getDifficultyText: function(difficulty) {
    const difficultyMap = {
      'easy': '简单',
      'normal': '普通',
      'hard': '困难'
    }
    return difficultyMap[difficulty] || '普通'
  },

  /**
   * 编辑用户信息
   */
  editUserInfo: function() {
    wx.showModal({
      title: '编辑用户信息',
      content: '请输入孩子的姓名',
      editable: true,
      placeholderText: this.data.userInfo.name || '小朋友',
      success: (res) => {
        if (res.confirm && res.content) {
          this.updateUserInfo({ name: res.content.trim() })
        }
      }
    })
  },

  /**
   * 更新用户信息
   */
  updateUserInfo: function(newInfo) {
    try {
      const userInfo = { ...this.data.userInfo, ...newInfo }
      wx.setStorageSync('userInfo', userInfo)
      this.setData({ userInfo })
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('保存用户信息失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 音效开关切换
   */
  onSoundToggle: function(e) {
    const soundEnabled = e.detail.value
    this.updateSettings({ soundEnabled })
  },

  /**
   * 自动下一个开关切换
   */
  onAutoNextToggle: function(e) {
    const autoNext = e.detail.value
    this.updateSettings({ autoNext })
  },

  /**
   * 更改学习难度
   */
  changeDifficulty: function() {
    const difficulties = ['easy', 'normal', 'hard']
    const difficultyTexts = ['简单', '普通', '困难']
    const currentIndex = difficulties.indexOf(this.data.settings.difficulty)
    
    wx.showActionSheet({
      itemList: difficultyTexts,
      success: (res) => {
        const newDifficulty = difficulties[res.tapIndex]
        this.updateSettings({ 
          difficulty: newDifficulty 
        })
        this.setData({
          difficultyText: difficultyTexts[res.tapIndex]
        })
      }
    })
  },

  /**
   * 更新设置
   */
  updateSettings: function(newSettings) {
    try {
      const settings = { ...this.data.settings, ...newSettings }
      
      // 更新本地数据
      this.setData({ settings })
      
      // 保存到学习数据中
      const learnData = app.getLearnData()
      learnData.settings = settings
      app.saveLearnData(learnData)
      
      wx.showToast({
        title: '设置已保存',
        icon: 'success'
      })
    } catch (error) {
      console.error('保存设置失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 导出学习数据
   */
  exportData: function() {
    try {
      const learnData = app.getLearnData()
      const dataStr = JSON.stringify(learnData, null, 2)
      
      // 在小程序中，可以通过分享或其他方式导出数据
      wx.showModal({
        title: '导出数据',
        content: '学习数据已准备好，可以通过分享功能发送给其他设备',
        success: (res) => {
          if (res.confirm) {
            // 这里可以实现具体的导出逻辑
            console.log('导出数据:', dataStr)
          }
        }
      })
    } catch (error) {
      console.error('导出数据失败:', error)
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      })
    }
  },

  /**
   * 导入学习数据
   */
  importData: function() {
    wx.showModal({
      title: '导入数据',
      content: '此功能将覆盖当前的学习数据，是否继续？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以实现具体的导入逻辑
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  /**
   * 清空学习数据
   */
  clearData: function() {
    wx.showModal({
      title: '清空数据',
      content: '此操作将删除所有学习记录，且不可恢复，确定要继续吗？',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.confirmClearData()
        }
      }
    })
  },

  /**
   * 确认清空数据
   */
  confirmClearData: function() {
    try {
      // 清空学习数据
      wx.removeStorageSync('learnData')
      wx.removeStorageSync('todayCompleted')
      wx.removeStorageSync('lastLearnDate')
      wx.removeStorageSync('learnProgress')
      wx.removeStorageSync('reviewProgress')
      wx.removeStorageSync('recallProgress')
      wx.removeStorageSync('learnHistory')
      
      // 重新初始化
      app.initStorage()
      
      wx.showToast({
        title: '数据已清空',
        icon: 'success'
      })
      
      // 刷新页面数据
      this.loadSettings()
    } catch (error) {
      console.error('清空数据失败:', error)
      wx.showToast({
        title: '清空失败',
        icon: 'none'
      })
    }
  },

  /**
   * 显示隐私政策
   */
  showPrivacy: function() {
    wx.showModal({
      title: '隐私政策',
      content: '我们重视您的隐私保护。本应用仅在本地存储学习数据，不会上传到服务器。',
      showCancel: false
    })
  },

  /**
   * 显示使用条款
   */
  showTerms: function() {
    wx.showModal({
      title: '使用条款',
      content: '本应用仅供学习使用，请在家长监护下使用。我们不对学习效果做任何保证。',
      showCancel: false
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '儿童识字 - 让学习变得简单有趣',
      path: '/pages/home/<USER>'
    }
  }
})
