/**
 * 复习模式页面样式
 */

.container {
  height: 100vh;
  overflow: hidden;
}

.progress-section {
  padding: 30rpx 40rpx;
  color: white;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 28rpx;
  opacity: 0.9;
}

.progress-stats {
  font-size: 24rpx;
  opacity: 0.8;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255,255,255,0.3);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.main-content {
  height: calc(100vh - 94rpx - 140rpx);
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.word-display-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.word-card {
  background: white;
  border-radius: 40rpx;
  padding: 80rpx 40rpx 60rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.1);
}

.word-text {
  display: block;
  font-size: 120rpx;
  font-weight: bold;
  color: #fa709a;
  margin-bottom: 30rpx;
  text-shadow: 0 4rpx 8rpx rgba(250, 112, 154, 0.3);
}

.word-question {
  font-size: 32rpx;
  color: #666;
  font-weight: 600;
}

.choice-buttons {
  display: flex;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.choice-btn {
  flex: 1;
  background: white;
  border: none;
  border-radius: 30rpx;
  padding: 50rpx 30rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.choice-btn:active {
  transform: scale(0.95);
}

.known-btn {
  border: 4rpx solid #4CAF50;
}

.unknown-btn {
  border: 4rpx solid #f44336;
}

.choice-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.choice-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.known-btn .choice-text {
  color: #4CAF50;
}

.unknown-btn .choice-text {
  color: #f44336;
}

.choice-hint {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.review-tips {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.tips-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.tips-content {
  color: white;
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.6;
}

.tip-item {
  display: block;
  margin-bottom: 8rpx;
}

.icon-text {
  font-size: inherit;
}
