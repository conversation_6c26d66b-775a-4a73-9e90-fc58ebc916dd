# 微信小程序功能设计 (版本1.0)

## 1. 核心学习流程

### 1.1 每日学习准备
- **字词输入**：提供一个简单的界面，提醒用户手动输入今日要学习的6个字词。

### 1.2 学习阶段
- **字词展示**：点击“开始学习”后，小程序挨个展示输入的6个字词。
- **图片辅助**：每个字词配以清晰的图片。
- **家长引导**：此阶段不提供自动发音，由家长引导小孩发音和理解字词。

### 1.3 复习阶段
- **字词回顾**：学完一遍后，进入复习阶段，挨个显示学习过的字词。
- **认识判断**：小孩自己判断是否认识该字词，并选择“✅”（认识）或“❌”（不认识）。

### 1.4 回顾阶段
- **不认识字词回顾**：复习完成后，针对所有被标记为“❌”的字词，进入回顾阶段。
- **再次展示**：小程序挨个展示这些不认识的字词，由家长再次引导。
- **不强求掌握**：此阶段不进行再次判断，过完所有不认识的字词后，学习流程结束，不强求小孩立刻掌握。

## 2. 学习进度保存
- **本地保存**：学习进度（如已学习的字词、认识/不认识的标记）将保存在本地，确保离线可用。

## 3. 离线使用
- **核心功能离线**：版本1.0的核心学习、复习、回顾功能均支持离线使用，无需网络连接。
