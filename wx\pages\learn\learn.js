/**
 * 学习模式页面逻辑
 * 功能：逐个展示字词，引导学习
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    todayWords: [], // 今日要学习的字词
    currentIndex: 0, // 当前字词索引
    currentWord: '', // 当前字词
    currentPinyin: '', // 当前字词拼音
    totalWords: 0, // 总字词数
    progressPercent: 0, // 进度百分比
    isLastWord: false // 是否是最后一个字词
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('学习模式页加载')
    this.loadTodayWords()
  },

  /**
   * 加载今日字词
   */
  loadTodayWords: function() {
    try {
      const learnData = app.getLearnData()
      const todayWords = learnData.todayWords || []
      
      if (todayWords.length === 0) {
        wx.showModal({
          title: '提示',
          content: '没有找到今日学习的字词，请先输入字词',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/input/input'
              })
            } else {
              wx.navigateBack()
            }
          }
        })
        return
      }

      this.setData({
        todayWords: todayWords,
        totalWords: todayWords.length,
        currentWord: todayWords[0] || '',
        currentPinyin: this.getPinyin(todayWords[0] || ''),
        progressPercent: 0,
        isLastWord: todayWords.length === 1
      })
    } catch (error) {
      console.error('加载今日字词失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 获取字词拼音（简单实现，实际项目中可以接入拼音API）
   */
  getPinyin: function(word) {
    // 这里可以接入拼音转换API
    // 暂时返回空字符串
    return ''
  },

  /**
   * 上一个字词
   */
  previousWord: function() {
    if (this.data.currentIndex > 0) {
      const newIndex = this.data.currentIndex - 1
      this.updateCurrentWord(newIndex)
    }
  },

  /**
   * 下一个字词
   */
  nextWord: function() {
    if (this.data.isLastWord) {
      // 完成学习，跳转到复习页面
      this.completeLearn()
    } else {
      const newIndex = this.data.currentIndex + 1
      this.updateCurrentWord(newIndex)
    }
  },

  /**
   * 更新当前字词
   */
  updateCurrentWord: function(index) {
    const { todayWords, totalWords } = this.data
    
    if (index >= 0 && index < totalWords) {
      const currentWord = todayWords[index]
      const progressPercent = Math.round(((index + 1) / totalWords) * 100)
      
      this.setData({
        currentIndex: index,
        currentWord: currentWord,
        currentPinyin: this.getPinyin(currentWord),
        progressPercent: progressPercent,
        isLastWord: index === totalWords - 1
      })
    }
  },

  /**
   * 完成学习
   */
  completeLearn: function() {
    try {
      // 更新学习数据
      const learnData = app.getLearnData()
      
      // 添加到已学字词列表
      const learnedWords = learnData.learnedWords || []
      const newWords = this.data.todayWords.filter(word => !learnedWords.includes(word))
      learnData.learnedWords = [...learnedWords, ...newWords]
      
      // 更新学习天数
      const today = new Date().toDateString()
      const lastLearnDate = wx.getStorageSync('lastLearnDate')
      if (lastLearnDate !== today) {
        learnData.learnDays = (learnData.learnDays || 0) + 1
        wx.setStorageSync('lastLearnDate', today)
      }
      
      // 更新总字词数
      learnData.totalWords = learnData.learnedWords.length
      
      app.saveLearnData(learnData)
      
      // 跳转到复习页面
      wx.redirectTo({
        url: '/pages/review/review',
        success: () => {
          console.log('跳转到复习页面成功')
        },
        fail: (error) => {
          console.error('跳转失败:', error)
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          })
        }
      })
    } catch (error) {
      console.error('完成学习失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 页面返回事件
   */
  onUnload: function() {
    // 保存学习进度
    try {
      wx.setStorageSync('learnProgress', {
        currentIndex: this.data.currentIndex,
        timestamp: Date.now()
      })
    } catch (error) {
      console.error('保存学习进度失败:', error)
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: `正在学习：${this.data.currentWord}`,
      path: '/pages/learn/learn'
    }
  }
})
