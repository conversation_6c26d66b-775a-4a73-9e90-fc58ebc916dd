<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 47px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            backdrop-filter: blur(10px);
        }
        
        .header-title {
            font-size: 20px;
            font-weight: bold;
        }
        
        .main-content {
            height: calc(100vh - 47px - 80px - 80px);
            overflow-y: auto;
            padding: 0 20px 20px;
        }
        
        .stats-overview {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .achievement-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .achievement-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .achievement-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: white;
        }
        
        .achievement-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .achievement-text {
            font-size: 14px;
            font-weight: 600;
        }
        
        .history-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .history-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-date {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
        }
        
        .date-day {
            font-size: 18px;
            font-weight: bold;
        }
        
        .date-month {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .history-content {
            flex: 1;
        }
        
        .history-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .history-details {
            font-size: 14px;
            color: #666;
        }
        
        .history-stats {
            text-align: right;
            color: #667eea;
            font-weight: 600;
        }
        
        .words-learned {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .word-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .word-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .word-tag.difficult {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .bottom-nav {
            height: 80px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #667eea;
            font-size: 12px;
            font-weight: 600;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-item.inactive {
            color: #999;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div style="display: flex; gap: 5px;">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>
    
    <!-- 页面头部 -->
    <div class="header">
        <div class="header-left">
            <div class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </div>
            <div class="header-title">学习记录</div>
        </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">24</div>
                    <div class="stat-label">总学字词</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">4</div>
                    <div class="stat-label">学习天数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">18</div>
                    <div class="stat-label">已掌握</div>
                </div>
            </div>
            <div style="text-align: center; font-size: 14px; opacity: 0.8;">
                掌握率：75% 📈
            </div>
        </div>
        
        <!-- 成就展示 -->
        <div class="achievement-section">
            <div class="section-title">
                <i class="fas fa-trophy"></i>
                学习成就
            </div>
            <div class="achievement-grid">
                <div class="achievement-item">
                    <div class="achievement-icon">🏆</div>
                    <div class="achievement-text">连续学习4天</div>
                </div>
                <div class="achievement-item">
                    <div class="achievement-icon">⭐</div>
                    <div class="achievement-text">掌握20个字词</div>
                </div>
            </div>
        </div>
        
        <!-- 学习历史 -->
        <div class="history-section">
            <div class="section-title">
                <i class="fas fa-history"></i>
                学习历史
            </div>
            
            <div class="history-item">
                <div class="history-date">
                    <div class="date-day">15</div>
                    <div class="date-month">今天</div>
                </div>
                <div class="history-content">
                    <div class="history-title">今日学习</div>
                    <div class="history-details">学习6个字词，掌握4个</div>
                </div>
                <div class="history-stats">67%</div>
            </div>
            
            <div class="history-item">
                <div class="history-date">
                    <div class="date-day">14</div>
                    <div class="date-month">昨天</div>
                </div>
                <div class="history-content">
                    <div class="history-title">昨日学习</div>
                    <div class="history-details">学习6个字词，掌握5个</div>
                </div>
                <div class="history-stats">83%</div>
            </div>
            
            <div class="history-item">
                <div class="history-date">
                    <div class="date-day">13</div>
                    <div class="date-month">3月</div>
                </div>
                <div class="history-content">
                    <div class="history-title">前日学习</div>
                    <div class="history-details">学习6个字词，掌握6个</div>
                </div>
                <div class="history-stats">100%</div>
            </div>
        </div>
        
        <!-- 已学字词 -->
        <div class="words-learned">
            <div class="section-title">
                <i class="fas fa-book-open"></i>
                已学字词
            </div>
            <div class="word-tags">
                <div class="word-tag">苹果</div>
                <div class="word-tag">香蕉</div>
                <div class="word-tag difficult">橙子</div>
                <div class="word-tag">葡萄</div>
                <div class="word-tag">西瓜</div>
                <div class="word-tag">草莓</div>
                <div class="word-tag">桃子</div>
                <div class="word-tag difficult">菠萝</div>
                <div class="word-tag">樱桃</div>
                <div class="word-tag">柠檬</div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item inactive">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-book"></i>
            <span>学习</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-chart-line"></i>
            <span>进度</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
