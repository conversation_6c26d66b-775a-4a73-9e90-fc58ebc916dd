/**
 * 学习记录页面样式
 */

.container {
  min-height: 100vh;
}

.main-content {
  height: calc(100vh - 94rpx);
  padding: 40rpx;
}

.achievement-section {
  margin-bottom: 40rpx;
}

.achievement-card {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
}

.achievement-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.achievement-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.section-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.today-section, .history-section, .learned-section {
  margin-bottom: 40rpx;
}

.today-card, .history-item {
  background: white;
  border-radius: 30rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.today-header, .history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.today-date, .history-date {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.today-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.today-status.completed {
  background: #e8f5e8;
  color: #4CAF50;
}

.today-status.pending {
  background: #fff3e0;
  color: #ff9800;
}

.history-stats {
  font-size: 24rpx;
  color: #666;
}

.history-count {
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.word-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.word-tag {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.word-tag.small {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.word-tag.learned {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.learned-words {
  background: white;
  border-radius: 30rpx;
  padding: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  color: white;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 30rpx;
}

.btn-small {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  background: white;
  color: #667eea;
  border-radius: 25rpx;
}

.icon-text {
  font-size: inherit;
}
