/**
 * 字词输入页面逻辑
 * 功能：输入今日要学习的6个字词
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    wordInputs: ['', '', '', '', '', ''], // 6个字词输入框
    canStart: false // 是否可以开始学习
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('字词输入页加载')
    this.loadTodayWords()
  },

  /**
   * 加载今日字词
   */
  loadTodayWords: function() {
    try {
      const learnData = app.getLearnData()
      if (learnData.todayWords && learnData.todayWords.length > 0) {
        this.setData({
          wordInputs: learnData.todayWords.slice(0, 6).concat(Array(6).fill('')).slice(0, 6)
        })
        this.checkCanStart()
      }
    } catch (error) {
      console.error('加载今日字词失败:', error)
    }
  },

  /**
   * 字词输入事件
   */
  onWordInput: function(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value.trim()
    
    const wordInputs = [...this.data.wordInputs]
    wordInputs[index] = value
    
    this.setData({
      wordInputs: wordInputs
    })
    
    this.checkCanStart()
  },

  /**
   * 输入确认事件
   */
  onInputConfirm: function(e) {
    const index = e.currentTarget.dataset.index
    // 自动聚焦到下一个输入框
    if (index < 5) {
      // 微信小程序中需要通过其他方式实现自动聚焦
      console.log('移动到下一个输入框')
    }
  },

  /**
   * 检查是否可以开始学习
   */
  checkCanStart: function() {
    const validWords = this.data.wordInputs.filter(word => word.length > 0)
    this.setData({
      canStart: validWords.length >= 3 // 至少输入3个字词才能开始
    })
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack({
      delta: 1,
      success: () => {
        console.log('返回成功')
      },
      fail: (error) => {
        console.error('返回失败:', error)
        // 如果返回失败，跳转到首页
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    })
  },

  /**
   * 开始学习
   */
  startLearning: function() {
    if (!this.data.canStart) {
      wx.showToast({
        title: '请至少输入3个字词',
        icon: 'none'
      })
      return
    }

    // 过滤空字词
    const validWords = this.data.wordInputs.filter(word => word.length > 0)
    
    if (validWords.length === 0) {
      wx.showToast({
        title: '请输入要学习的字词',
        icon: 'none'
      })
      return
    }

    // 保存今日字词
    this.saveTodayWords(validWords)

    // 跳转到学习页面
    wx.navigateTo({
      url: '/pages/learn/learn',
      success: () => {
        console.log('跳转到学习页面成功')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 保存今日字词
   */
  saveTodayWords: function(words) {
    try {
      const learnData = app.getLearnData()
      learnData.todayWords = words
      
      // 更新总字词数（去重）
      const allWords = [...(learnData.learnedWords || []), ...words]
      const uniqueWords = [...new Set(allWords)]
      learnData.totalWords = uniqueWords.length
      
      app.saveLearnData(learnData)
      
      wx.showToast({
        title: '字词保存成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('保存字词失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadTodayWords()
    wx.stopPullDownRefresh()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '儿童识字 - 今日学习准备',
      path: '/pages/input/input'
    }
  }
})
