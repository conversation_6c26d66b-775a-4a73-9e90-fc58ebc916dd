/**
 * 儿童识字小程序 - 全局样式
 * 基于原HTML原型的设计，适配微信小程序
 */

/* 全局字体设置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  font-size: 32rpx;
  line-height: 1.6;
}

/* 容器样式 */
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 渐变背景样式 */
.gradient-bg-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-pink {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-cyan {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-orange {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.gradient-bg-purple {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* 状态栏样式 */
.status-bar {
  height: 94rpx;
  background: rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.status-icons {
  display: flex;
  gap: 10rpx;
}

/* 主要内容区域 */
.main-content {
  padding: 40rpx;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 40rpx;
  padding: 50rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.1);
}

.glass-card {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 50rpx;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.btn-primary {
  background: white;
  color: #667eea;
  padding: 36rpx 60rpx;
  font-size: 36rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);
}

.btn-secondary {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 4rpx solid rgba(255,255,255,0.3);
  padding: 32rpx 60rpx;
  font-size: 32rpx;
  backdrop-filter: blur(20rpx);
}

.btn-large {
  padding: 40rpx 80rpx;
  font-size: 40rpx;
}

.btn-small {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 输入框样式 */
.input {
  border: 4rpx solid #f0f0f0;
  border-radius: 24rpx;
  padding: 30rpx;
  font-size: 36rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

/* 文本样式 */
.text-white {
  color: white;
}

.text-center {
  text-align: center;
}

.text-large {
  font-size: 48rpx;
  font-weight: bold;
}

.text-medium {
  font-size: 36rpx;
  font-weight: 600;
}

.text-small {
  font-size: 28rpx;
}

.text-opacity {
  opacity: 0.9;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

.gap-small {
  gap: 20rpx;
}

.gap-medium {
  gap: 30rpx;
}

.gap-large {
  gap: 40rpx;
}

/* 间距样式 */
.mt-small { margin-top: 20rpx; }
.mt-medium { margin-top: 40rpx; }
.mt-large { margin-top: 60rpx; }

.mb-small { margin-bottom: 20rpx; }
.mb-medium { margin-bottom: 40rpx; }
.mb-large { margin-bottom: 60rpx; }

.p-small { padding: 20rpx; }
.p-medium { padding: 40rpx; }
.p-large { padding: 60rpx; }

/* 图标样式 */
.icon {
  font-size: 40rpx;
}

.icon-large {
  font-size: 80rpx;
}

.icon-small {
  font-size: 32rpx;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(60rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .main-content {
    padding: 30rpx;
  }
  
  .card {
    padding: 40rpx;
  }
  
  .btn-primary {
    font-size: 32rpx;
    padding: 32rpx 50rpx;
  }
}
