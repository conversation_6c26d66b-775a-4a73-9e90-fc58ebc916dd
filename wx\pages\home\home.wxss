/**
 * 首页样式
 * 基于原HTML设计，适配微信小程序
 */

.container {
  height: 100vh;
  overflow: hidden;
}

.main-content {
  height: calc(100vh - 94rpx);
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.welcome-section {
  text-align: center;
  color: white;
}

.app-icon {
  width: 160rpx;
  height: 160rpx;
  background: white;
  border-radius: 40rpx;
  margin: 0 auto 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);
}

.icon-text {
  font-size: inherit;
}

.welcome-text {
  margin-bottom: 60rpx;
}

.welcome-title {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.welcome-subtitle {
  display: block;
  font-size: 32rpx;
  opacity: 0.9;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 60rpx;
}

.stat-card {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.btn-primary {
  background: white;
  color: #667eea;
}

.btn-secondary {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 4rpx solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20rpx);
}
