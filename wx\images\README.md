# 图标资源说明

## 底部导航图标

已为儿童识字小程序创建了完整的图标集合。

### 图标规格
- **尺寸**：81px × 81px
- **格式**：SVG（需转换为PNG）
- **背景**：透明
- **设计风格**：简洁线条，儿童友好

### 已创建的图标文件

#### SVG源文件
1. `home.svg` / `home-active.svg` - 首页图标（房子造型）
2. `learn.svg` / `learn-active.svg` - 学习图标（书本+对勾）
3. `progress.svg` / `progress-active.svg` - 进度图标（清单+时钟）
4. `user.svg` / `user-active.svg` - 用户图标（可爱头像）

#### 图标设计特点
- **首页图标**：温馨的房子造型，带有小圆点装饰
- **学习图标**：打开的书本配合对勾，表示学习和完成
- **进度图标**：任务清单配合时钟，表示进度和时间
- **用户图标**：可爱的儿童头像，带有笑脸表情

### 转换为PNG的方法

#### 方法一：使用在线工具
1. 访问 https://convertio.co/svg-png/ 或类似网站
2. 上传SVG文件
3. 设置输出尺寸为 81x81px
4. 下载PNG文件

#### 方法二：使用设计软件
1. 在 Figma/Sketch/Adobe Illustrator 中打开SVG
2. 导出为PNG，尺寸设置为 81x81px
3. 确保背景透明

#### 方法三：使用命令行工具
```bash
# 使用 ImageMagick
convert -background none -size 81x81 home.svg home.png

# 使用 Inkscape
inkscape --export-png=home.png --export-width=81 --export-height=81 home.svg
```

### 颜色方案
- **未选中状态**：#999999（灰色）
- **选中状态**：#667eea（主题蓝色）
- **激活状态**：添加了淡色填充效果

### 使用说明
1. 将SVG文件转换为对应的PNG文件
2. 确保文件名正确匹配 app.json 中的配置
3. PNG文件应保持透明背景
4. 图标会自动根据选中状态改变颜色
